#!/usr/bin/env python3
"""
Orders Agent - Sistema de gestión de órdenes con capacidades RAG

Este módulo proporciona un agente especializado en gestión de órdenes que puede:
- Consultar órdenes por estado (pendientes, completadas, canceladas)
- Buscar órdenes por fecha y rango de fechas
- Consultar órdenes por cliente
- Proporcionar estadísticas de órdenes
- Responder preguntas en lenguaje natural sobre órdenes

Autor: Agente V5
Fecha: 2025-01-10
"""

from .database import orders_db_manager
from .order_tools import (
    search_orders_by_status_function,
    search_orders_by_date_function,
    search_orders_by_customer_function,
    get_order_statistics_function,
    order_query_tools
)

__all__ = [
    "orders_db_manager",
    "search_orders_by_status_function",
    "search_orders_by_date_function", 
    "search_orders_by_customer_function",
    "get_order_statistics_function",
    "order_query_tools"
]