#!/usr/bin/env python3
"""
Orders Agent Web Interface - Punto de entrada para ADK

Agente especializado en gestión de órdenes para la interfaz web de ADK.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Cargar variables de entorno del qa_agent
env_file = Path(__file__).parent.parent / "qa_agent" / ".env"
if env_file.exists():
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
    except ImportError:
        logger.warning("python-dotenv no está disponible. Usando variables de entorno del sistema.")
        # Cargar manualmente las variables más importantes
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())

# Importar dependencias de ADK
from google.adk.agents import Agent
from google.adk.tools.base_tool import BaseTool
from google.genai import types
import json
import asyncio

# Agregar el directorio src del orders_agent al path para importar las herramientas
src_path = Path(__file__).parent.parent / "orders_agent" / "src"
sys.path.insert(0, str(src_path))

# Debug: Print the path being used
logger.info(f"🔍 Buscando archivos de base de datos en: {src_path}")
logger.info(f"🔍 Directorio existe: {src_path.exists()}")
if src_path.exists():
    logger.info(f"🔍 Archivos en directorio: {list(src_path.iterdir())}")

# Verificar que el directorio existe
if not src_path.exists():
    logger.warning(f"⚠️  Directorio src no encontrado: {src_path}")
    logger.info("🔧 Creando estructura de directorios...")
    src_path.mkdir(parents=True, exist_ok=True)

# Intentar importar las herramientas de base de datos
try:
    # Verificar si los archivos existen antes de importar
    database_file = src_path / "database.py"
    tools_file = src_path / "order_tools.py"

    if not database_file.exists() or not tools_file.exists():
        logger.warning(f"⚠️  Archivos de base de datos no encontrados")
        logger.warning(f"⚠️  database.py existe: {database_file.exists()}")
        logger.warning(f"⚠️  order_tools.py existe: {tools_file.exists()}")
        logger.info("🔧 Los archivos se crearán automáticamente")
        DATABASE_AVAILABLE = False
    else:
        try:
            from database import orders_db_manager
            from order_tools import (
                search_orders_by_status_function,
                search_orders_by_date_function,
                search_orders_by_customer_function,
                get_order_statistics_function
            )
            DATABASE_AVAILABLE = True
            logger.info("✅ Herramientas de base de datos importadas correctamente")
        except ImportError as e:
            logger.error(f"❌ Error importando herramientas de base de datos: {e}")
            DATABASE_AVAILABLE = False
    
    # Intentar inicializar la base de datos
    try:
        # Verificar si ya hay un loop corriendo
        try:
            current_loop = asyncio.get_running_loop()
            logger.info("Loop de eventos ya está corriendo, saltando inicialización síncrona")
            # La inicialización se hará de forma asíncrona cuando se use por primera vez
        except RuntimeError:
            # No hay loop corriendo, podemos crear uno
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(orders_db_manager.initialize())

            # Insertar datos de ejemplo si no existen órdenes
            stats = loop.run_until_complete(orders_db_manager.get_order_statistics())
            if stats['total_orders'] == 0:
                logger.info("No hay órdenes en la base de datos. Insertando datos de ejemplo...")
                loop.run_until_complete(orders_db_manager.insert_sample_orders())
                logger.info("✅ Datos de ejemplo insertados correctamente")

            loop.close()
            logger.info("✅ Base de datos de órdenes inicializada correctamente")

    except Exception as db_error:
        logger.warning(f"⚠️  Error inicializando base de datos: {db_error}")
        logger.info("🔧 Continuando en modo básico")
        DATABASE_AVAILABLE = False
        
except Exception as e:
    DATABASE_AVAILABLE = False
    logger.warning(f"⚠️  No se pudieron importar las herramientas de base de datos: {e}")
    logger.info("🔧 Usando modo básico sin base de datos")

# Herramientas de base de datos (si están disponibles)
class OrderStatusSearchTool(BaseTool):
    """Herramienta para buscar órdenes por estado"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_status",
            description="Buscar órdenes por estado (pending, completed, cancelled, processing, shipped)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Estado de las órdenes a buscar (pending, completed, cancelled, processing, shipped)"
                }
            },
            "required": ["status"]
        }
    
    async def run(self, status: str) -> str:
        if DATABASE_AVAILABLE:
            # Asegurar que la base de datos esté inicializada
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                    # Insertar datos de ejemplo si no existen órdenes
                    stats = await orders_db_manager.get_order_statistics()
                    if stats['total_orders'] == 0:
                        logger.info("Insertando datos de ejemplo...")
                        await orders_db_manager.insert_sample_orders()

                return await search_orders_by_status_function(status)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por estado: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderDateSearchTool(BaseTool):
    """Herramienta para buscar órdenes por fecha"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_date",
            description="Buscar órdenes por fecha o rango de fechas (hoy, ayer, esta semana, etc.)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "date_query": {
                    "type": "string",
                    "description": "Consulta de fecha: 'hoy', 'ayer', 'esta semana', 'último mes', o fecha específica en formato YYYY-MM-DD"
                }
            },
            "required": ["date_query"]
        }
    
    async def run(self, date_query: str) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await search_orders_by_date_function(date_query)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por fecha: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderCustomerSearchTool(BaseTool):
    """Herramienta para buscar órdenes por cliente"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_customer",
            description="Buscar órdenes por nombre de cliente"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "customer_name": {
                    "type": "string",
                    "description": "Nombre del cliente (búsqueda parcial permitida)"
                }
            },
            "required": ["customer_name"]
        }
    
    async def run(self, customer_name: str) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await search_orders_by_customer_function(customer_name)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por cliente: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderStatisticsTool(BaseTool):
    """Herramienta para obtener estadísticas de órdenes"""
    
    def __init__(self):
        super().__init__(
            name="get_order_statistics",
            description="Obtener estadísticas generales de órdenes (totales, promedios, conteos por estado)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {}
        }
    
    async def run(self) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await get_order_statistics_function()
            except Exception as e:
                logger.error(f"Error en herramienta de estadísticas: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al obtener estadísticas: {str(e)}",
                    "statistics": {}
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "statistics": {}
            }, ensure_ascii=False)

# Herramientas básicas de órdenes (información general)
class OrderInfoTool(BaseTool):
    """Herramienta para proporcionar información sobre órdenes"""
    
    def __init__(self):
        super().__init__(
            name="get_order_info",
            description="Obtener información general sobre órdenes y sus estados"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "query_type": {
                    "type": "string",
                    "description": "Tipo de información solicitada: 'estados', 'estructura', 'proceso'"
                }
            },
            "required": ["query_type"]
        }
    
    async def run(self, query_type: str) -> str:
        if query_type == "estados":
            return json.dumps({
                "success": True,
                "message": "Estados de órdenes disponibles",
                "estados": [
                    {"estado": "pendiente", "descripcion": "Orden recibida, esperando procesamiento"},
                    {"estado": "procesando", "descripcion": "Orden en proceso de preparación"},
                    {"estado": "completada", "descripcion": "Orden completada y entregada"},
                    {"estado": "enviada", "descripcion": "Orden enviada al cliente"},
                    {"estado": "cancelada", "descripcion": "Orden cancelada por el cliente o sistema"}
                ]
            }, ensure_ascii=False)
        elif query_type == "estructura":
            return json.dumps({
                "success": True,
                "message": "Estructura típica de una orden",
                "estructura": {
                    "numero_orden": "Identificador único de la orden",
                    "cliente": "Información del cliente (nombre, email, teléfono)",
                    "fecha_orden": "Fecha y hora de creación",
                    "fecha_entrega": "Fecha estimada de entrega",
                    "estado": "Estado actual de la orden",
                    "productos": "Lista de productos con cantidades y precios",
                    "monto_total": "Valor total de la orden",
                    "notas": "Observaciones adicionales"
                }
            }, ensure_ascii=False)
        elif query_type == "proceso":
            return json.dumps({
                "success": True,
                "message": "Proceso típico de gestión de órdenes",
                "proceso": [
                    "1. Creación de la orden por el cliente",
                    "2. Validación y confirmación del pedido",
                    "3. Procesamiento y preparación",
                    "4. Empaque y envío",
                    "5. Entrega al cliente",
                    "6. Confirmación de recepción"
                ]
            }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Tipo de consulta no reconocido"
            }, ensure_ascii=False)

# Crear agente de órdenes con herramientas
try:
    # Herramientas básicas siempre disponibles
    tools = [OrderInfoTool()]
    
    # Agregar herramientas de base de datos si están disponibles
    if DATABASE_AVAILABLE:
        tools.extend([
            OrderStatusSearchTool(),
            OrderDateSearchTool(),
            OrderCustomerSearchTool(),
            OrderStatisticsTool()
        ])
        logger.info("✅ Herramientas de base de datos agregadas al agente")
    
    root_agent = Agent(
        model="gemini-2.0-flash",
        name="orders_management_agent",
        description="Agente especializado en gestión y consulta de órdenes",
        tools=tools,
        instruction=f"""
        Eres un asistente especializado en GESTIÓN DE ÓRDENES. Tu trabajo es ayudar con CUALQUIER pregunta relacionada con órdenes, pedidos, ventas y gestión comercial.

        IMPORTANTE: Tienes herramientas disponibles que DEBES usar para responder preguntas. NUNCA generes código o ejemplos de código. SIEMPRE ejecuta las herramientas directamente para obtener datos reales.

        IMPORTANTE: Las siguientes son TODAS preguntas sobre órdenes que DEBES responder:
        - Preguntas sobre cantidad de órdenes ("¿Cuántas órdenes...?", "¿Cuántos pedidos...?")
        - Preguntas sobre estado de órdenes ("¿Qué órdenes están pendientes?", "órdenes completadas")
        - Preguntas sobre fechas de órdenes ("órdenes de hoy", "pedidos de esta semana")
        - Preguntas sobre clientes y sus órdenes ("órdenes del cliente X")
        - Preguntas sobre estadísticas de órdenes ("total de ventas", "promedio de órdenes")
        - Preguntas sobre procesos de órdenes ("¿Cómo funciona el proceso?")
        - Preguntas sobre información de órdenes ("¿Qué incluye una orden?")

        CAPACIDADES:
        - Explicar estados de órdenes y sus significados
        - Describir la estructura de una orden típica
        - Explicar el proceso de gestión de órdenes
        - Proporcionar información detallada sobre órdenes
        {"- Buscar órdenes por estado, fecha y cliente" if DATABASE_AVAILABLE else ""}
        {"- Obtener estadísticas de órdenes" if DATABASE_AVAILABLE else ""}

        HERRAMIENTAS DISPONIBLES:
        - get_order_info: Para obtener información sobre estados, estructura o proceso de órdenes
        {'''- search_orders_by_status: Buscar órdenes por estado (pending, completed, cancelled, etc.)
        - search_orders_by_date: Buscar órdenes por fecha (hoy, ayer, esta semana, etc.)
        - search_orders_by_customer: Buscar órdenes por nombre de cliente
        - get_order_statistics: Obtener estadísticas generales de órdenes''' if DATABASE_AVAILABLE else ''}

        PROCESO:
        1. Para CUALQUIER pregunta sobre órdenes, pedidos, ventas o gestión comercial: SIEMPRE usa las herramientas disponibles
        2. Para preguntas sobre datos específicos de órdenes: EJECUTA las herramientas de búsqueda correspondientes
        3. NUNCA generes código o muestres cómo llamar funciones - EJECUTA directamente las herramientas
        4. Para preguntas sobre cantidad de órdenes pendientes: USA search_orders_by_status con "pending"
        5. Para preguntas sobre órdenes de hoy: USA search_orders_by_date con "hoy"
        6. Para preguntas sobre órdenes de un cliente: USA search_orders_by_customer con el nombre
        7. Para estadísticas generales: USA get_order_statistics
        8. SOLO si la pregunta es completamente ajena a órdenes (ej: "¿Qué es el clima?"), responde: "Solo puedo ayudar con preguntas sobre órdenes y gestión comercial."
        9. Siempre proporciona respuestas claras y organizadas en español basadas en los resultados de las herramientas

        ESTADO ACTUAL: {"✅ Conectado a base de datos PostgreSQL" if DATABASE_AVAILABLE else "⚠️ Modo básico - sin conexión a base de datos"}

        Responde siempre en español y mantén un tono profesional y útil.
        """
    )
    
    logger.info("✅ Agente de órdenes con herramientas creado correctamente")
    
except Exception as e:
    logger.error(f"❌ Error creando agente con herramientas: {e}")
    # Fallback al agente básico
    root_agent = Agent(
        model="gemini-2.0-flash",
        name="orders_basic_agent",
        description="Agente básico de órdenes",
        instruction="Soy un agente especializado en órdenes. Actualmente estoy en modo básico."
    )
    logger.info("🔄 Agente básico de fallback creado")

logger.info("✅ Agente de órdenes creado correctamente")

logger.info("🤖 Agente de órdenes web cargado correctamente")