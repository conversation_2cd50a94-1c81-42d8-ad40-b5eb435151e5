#!/usr/bin/env python3
"""
Orders Agent Web Interface - Punto de entrada para ADK

Agente especializado en gestión de órdenes para la interfaz web de ADK.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Cargar variables de entorno del qa_agent
env_file = Path(__file__).parent.parent / "qa_agent" / ".env"
if env_file.exists():
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
    except ImportError:
        logger.warning("python-dotenv no está disponible. Usando variables de entorno del sistema.")
        # Cargar manualmente las variables más importantes
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())

# Importar dependencias de ADK
from google.adk.agents import Agent
from google.adk.tools.base_tool import BaseTool
from google.genai import types
import json
import asyncio

# Agregar el directorio src del orders_agent al path para importar las herramientas
src_path = Path(__file__).parent.parent / "orders_agent" / "src"
sys.path.insert(0, str(src_path))

# Debug: Print the path being used
logger.info(f"🔍 Buscando archivos de base de datos en: {src_path}")
logger.info(f"🔍 Directorio existe: {src_path.exists()}")
if src_path.exists():
    logger.info(f"🔍 Archivos en directorio: {list(src_path.iterdir())}")

# Verificar que el directorio existe
if not src_path.exists():
    logger.warning(f"⚠️  Directorio src no encontrado: {src_path}")
    logger.info("🔧 Creando estructura de directorios...")
    src_path.mkdir(parents=True, exist_ok=True)

# Intentar importar las herramientas de base de datos
try:
    # Verificar si los archivos existen antes de importar
    database_file = src_path / "database.py"
    tools_file = src_path / "order_tools.py"

    if not database_file.exists() or not tools_file.exists():
        logger.warning(f"⚠️  Archivos de base de datos no encontrados")
        logger.warning(f"⚠️  database.py existe: {database_file.exists()}")
        logger.warning(f"⚠️  order_tools.py existe: {tools_file.exists()}")
        logger.info("🔧 Los archivos se crearán automáticamente")
        DATABASE_AVAILABLE = False
    else:
        try:
            from database import orders_db_manager
            from order_tools import (
                search_orders_by_status_function,
                search_orders_by_date_function,
                search_orders_by_customer_function,
                get_order_statistics_function
            )
            DATABASE_AVAILABLE = True
            logger.info("✅ Herramientas de base de datos importadas correctamente")
        except ImportError as e:
            logger.error(f"❌ Error importando herramientas de base de datos: {e}")
            DATABASE_AVAILABLE = False
    
    # Intentar inicializar la base de datos
    try:
        # Verificar si ya hay un loop corriendo
        try:
            current_loop = asyncio.get_running_loop()
            logger.info("Loop de eventos ya está corriendo, saltando inicialización síncrona")
            # La inicialización se hará de forma asíncrona cuando se use por primera vez
        except RuntimeError:
            # No hay loop corriendo, podemos crear uno
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(orders_db_manager.initialize())

            # Insertar datos de ejemplo si no existen órdenes
            stats = loop.run_until_complete(orders_db_manager.get_order_statistics())
            if stats['total_orders'] == 0:
                logger.info("No hay órdenes en la base de datos. Insertando datos de ejemplo...")
                loop.run_until_complete(orders_db_manager.insert_sample_orders())
                logger.info("✅ Datos de ejemplo insertados correctamente")

            loop.close()
            logger.info("✅ Base de datos de órdenes inicializada correctamente")

    except Exception as db_error:
        logger.warning(f"⚠️  Error inicializando base de datos: {db_error}")
        logger.info("🔧 Continuando en modo básico")
        DATABASE_AVAILABLE = False
        
except Exception as e:
    DATABASE_AVAILABLE = False
    logger.warning(f"⚠️  No se pudieron importar las herramientas de base de datos: {e}")
    logger.info("🔧 Usando modo básico sin base de datos")

# Funciones simples para herramientas (en lugar de BaseTool)
async def search_orders_by_status(status: str) -> str:
    """Buscar órdenes por estado (pending, completed, cancelled, processing, shipped)"""
    if DATABASE_AVAILABLE:
        try:
            if not orders_db_manager.pool:
                await orders_db_manager.initialize()
            return await search_orders_by_status_function(status)
        except Exception as e:
            logger.error(f"Error en búsqueda por estado: {e}")
            return json.dumps({
                "success": False,
                "error": f"Error al buscar órdenes: {str(e)}",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)
    else:
        return json.dumps({
            "success": False,
            "error": "Base de datos no disponible",
            "orders": [],
            "count": 0
        }, ensure_ascii=False)

async def search_orders_by_date(date_query: str) -> str:
    """Buscar órdenes por fecha (hoy, ayer, esta semana, etc.)"""
    if DATABASE_AVAILABLE:
        try:
            if not orders_db_manager.pool:
                await orders_db_manager.initialize()
            return await search_orders_by_date_function(date_query)
        except Exception as e:
            logger.error(f"Error en búsqueda por fecha: {e}")
            return json.dumps({
                "success": False,
                "error": f"Error al buscar órdenes: {str(e)}",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)
    else:
        return json.dumps({
            "success": False,
            "error": "Base de datos no disponible",
            "orders": [],
            "count": 0
        }, ensure_ascii=False)

async def search_orders_by_customer(customer_name: str) -> str:
    """Buscar órdenes por nombre de cliente"""
    if DATABASE_AVAILABLE:
        try:
            if not orders_db_manager.pool:
                await orders_db_manager.initialize()
            return await search_orders_by_customer_function(customer_name)
        except Exception as e:
            logger.error(f"Error en búsqueda por cliente: {e}")
            return json.dumps({
                "success": False,
                "error": f"Error al buscar órdenes: {str(e)}",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)
    else:
        return json.dumps({
            "success": False,
            "error": "Base de datos no disponible",
            "orders": [],
            "count": 0
        }, ensure_ascii=False)

async def get_order_statistics() -> str:
    """Obtener estadísticas generales de órdenes"""
    if DATABASE_AVAILABLE:
        try:
            if not orders_db_manager.pool:
                await orders_db_manager.initialize()
            return await get_order_statistics_function()
        except Exception as e:
            logger.error(f"Error en estadísticas: {e}")
            return json.dumps({
                "success": False,
                "error": f"Error al obtener estadísticas: {str(e)}",
                "statistics": {}
            }, ensure_ascii=False)
    else:
        return json.dumps({
            "success": False,
            "error": "Base de datos no disponible",
            "statistics": {}
        }, ensure_ascii=False)

def test_tool(message: str) -> str:
    """Herramienta de prueba simple"""
    return f"✅ Herramienta ejecutada correctamente con mensaje: {message}"

# Herramientas de base de datos (si están disponibles)
class OrderStatusSearchTool(BaseTool):
    """Herramienta para buscar órdenes por estado"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_status",
            description="Buscar órdenes por estado (pending, completed, cancelled, processing, shipped)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Estado de las órdenes a buscar (pending, completed, cancelled, processing, shipped)"
                }
            },
            "required": ["status"]
        }
    
    async def run(self, status: str) -> str:
        if DATABASE_AVAILABLE:
            # Asegurar que la base de datos esté inicializada
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                    # Insertar datos de ejemplo si no existen órdenes
                    stats = await orders_db_manager.get_order_statistics()
                    if stats['total_orders'] == 0:
                        logger.info("Insertando datos de ejemplo...")
                        await orders_db_manager.insert_sample_orders()

                return await search_orders_by_status_function(status)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por estado: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderDateSearchTool(BaseTool):
    """Herramienta para buscar órdenes por fecha"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_date",
            description="Buscar órdenes por fecha o rango de fechas (hoy, ayer, esta semana, etc.)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "date_query": {
                    "type": "string",
                    "description": "Consulta de fecha: 'hoy', 'ayer', 'esta semana', 'último mes', o fecha específica en formato YYYY-MM-DD"
                }
            },
            "required": ["date_query"]
        }
    
    async def run(self, date_query: str) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await search_orders_by_date_function(date_query)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por fecha: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderCustomerSearchTool(BaseTool):
    """Herramienta para buscar órdenes por cliente"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_customer",
            description="Buscar órdenes por nombre de cliente"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "customer_name": {
                    "type": "string",
                    "description": "Nombre del cliente (búsqueda parcial permitida)"
                }
            },
            "required": ["customer_name"]
        }
    
    async def run(self, customer_name: str) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await search_orders_by_customer_function(customer_name)
            except Exception as e:
                logger.error(f"Error en herramienta de búsqueda por cliente: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al buscar órdenes: {str(e)}",
                    "orders": [],
                    "count": 0
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "orders": [],
                "count": 0
            }, ensure_ascii=False)

class OrderStatisticsTool(BaseTool):
    """Herramienta para obtener estadísticas de órdenes"""
    
    def __init__(self):
        super().__init__(
            name="get_order_statistics",
            description="Obtener estadísticas generales de órdenes (totales, promedios, conteos por estado)"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {}
        }
    
    async def run(self) -> str:
        if DATABASE_AVAILABLE:
            try:
                if not orders_db_manager.pool:
                    await orders_db_manager.initialize()
                return await get_order_statistics_function()
            except Exception as e:
                logger.error(f"Error en herramienta de estadísticas: {e}")
                return json.dumps({
                    "success": False,
                    "error": f"Error al obtener estadísticas: {str(e)}",
                    "statistics": {}
                }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Base de datos no disponible. Agente en modo básico.",
                "statistics": {}
            }, ensure_ascii=False)

# Herramientas básicas de órdenes (información general)
class OrderInfoTool(BaseTool):
    """Herramienta para proporcionar información sobre órdenes"""
    
    def __init__(self):
        super().__init__(
            name="get_order_info",
            description="Obtener información general sobre órdenes y sus estados"
        )
    
    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "query_type": {
                    "type": "string",
                    "description": "Tipo de información solicitada: 'estados', 'estructura', 'proceso'"
                }
            },
            "required": ["query_type"]
        }
    
    async def run(self, query_type: str) -> str:
        if query_type == "estados":
            return json.dumps({
                "success": True,
                "message": "Estados de órdenes disponibles",
                "estados": [
                    {"estado": "pendiente", "descripcion": "Orden recibida, esperando procesamiento"},
                    {"estado": "procesando", "descripcion": "Orden en proceso de preparación"},
                    {"estado": "completada", "descripcion": "Orden completada y entregada"},
                    {"estado": "enviada", "descripcion": "Orden enviada al cliente"},
                    {"estado": "cancelada", "descripcion": "Orden cancelada por el cliente o sistema"}
                ]
            }, ensure_ascii=False)
        elif query_type == "estructura":
            return json.dumps({
                "success": True,
                "message": "Estructura típica de una orden",
                "estructura": {
                    "numero_orden": "Identificador único de la orden",
                    "cliente": "Información del cliente (nombre, email, teléfono)",
                    "fecha_orden": "Fecha y hora de creación",
                    "fecha_entrega": "Fecha estimada de entrega",
                    "estado": "Estado actual de la orden",
                    "productos": "Lista de productos con cantidades y precios",
                    "monto_total": "Valor total de la orden",
                    "notas": "Observaciones adicionales"
                }
            }, ensure_ascii=False)
        elif query_type == "proceso":
            return json.dumps({
                "success": True,
                "message": "Proceso típico de gestión de órdenes",
                "proceso": [
                    "1. Creación de la orden por el cliente",
                    "2. Validación y confirmación del pedido",
                    "3. Procesamiento y preparación",
                    "4. Empaque y envío",
                    "5. Entrega al cliente",
                    "6. Confirmación de recepción"
                ]
            }, ensure_ascii=False)
        else:
            return json.dumps({
                "success": False,
                "error": "Tipo de consulta no reconocido"
            }, ensure_ascii=False)

# Crear agente de órdenes con herramientas
try:
    # Herramientas básicas siempre disponibles
    tools = [OrderInfoTool(), TestTool()]
    
    # Agregar herramientas de base de datos si están disponibles
    if DATABASE_AVAILABLE:
        tools.extend([
            OrderStatusSearchTool(),
            OrderDateSearchTool(),
            OrderCustomerSearchTool(),
            OrderStatisticsTool()
        ])
        logger.info("✅ Herramientas de base de datos agregadas al agente")
    
    root_agent = Agent(
        model="gemini-1.5-flash",
        name="orders_management_agent",
        description="Agente especializado en gestión y consulta de órdenes",
        tools=tools,
        instruction="""
        You are an orders management assistant. You help with questions about orders, sales, and commercial management.

        You have access to tools that can search for orders in a database. When users ask about orders, you must use these tools to get real data.

        For questions about pending orders (like "¿Cuántas órdenes están pendientes?"), call the search_orders_by_status tool with status="pending".
        For questions about today's orders, call the search_orders_by_date tool with date_query="hoy".
        For questions about customer orders, call the search_orders_by_customer tool with the customer name.
        For general statistics, call the get_order_statistics tool.

        If someone asks you to test the tools, call the test_tool with message="test".

        Always use the tools to get real data. Never generate code or examples.
        Respond in Spanish with the actual results from the database.
        """,
        generate_content_config=types.GenerateContentConfig(
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.OFF,
                ),
            ]
        ),
    )
    
    logger.info("✅ Agente de órdenes con herramientas creado correctamente")
    
except Exception as e:
    logger.error(f"❌ Error creando agente con herramientas: {e}")
    # Fallback al agente básico
    root_agent = Agent(
        model="gemini-2.0-flash",
        name="orders_basic_agent",
        description="Agente básico de órdenes",
        instruction="Soy un agente especializado en órdenes. Actualmente estoy en modo básico."
    )
    logger.info("🔄 Agente básico de fallback creado")

logger.info("✅ Agente de órdenes creado correctamente")

logger.info("🤖 Agente de órdenes web cargado correctamente")