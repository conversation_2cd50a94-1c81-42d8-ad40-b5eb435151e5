# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente Q&A especializado en Dropi con RAG - Punto de entrada para ADK
"""

import sys
import asyncio
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Agregar el directorio src al path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    # Importar el agente desde src
    from agent import root_agent, ensure_rag_initialized
    
    # Inicializar el sistema RAG al cargar el módulo
    try:
        # Ejecutar la inicialización de manera síncrona
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(ensure_rag_initialized())
        loop.close()
        logger.info("✅ Sistema RAG de Dropi inicializado correctamente")
    except Exception as e:
        logger.warning(f"⚠️  Advertencia: No se pudo inicializar el sistema RAG: {e}")
        logger.warning("   El agente funcionará sin capacidades RAG completas.")

    logger.info("🤖 Agente de Dropi cargado correctamente")
    
except Exception as e:
    logger.error(f"❌ Error cargando el agente de Dropi: {e}")
    # Fallback a un agente básico
    from google.adk.agents import Agent
    root_agent = Agent(
        model="gemini-2.0-flash",
        name="dropi_fallback_agent",
        description="Agente básico de fallback para Dropi",
        instruction="Solo puedo responder preguntas sobre Dropi. Por favor, pregúntame algo relacionado con Dropi."
    )
    logger.info("🔄 Agente de fallback cargado")
